/**
 * Simple test for remote browser connection with sessionId
 */

import { CDP } from "simple-cdp";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

const HYPERBROWSER_API_KEY = "hb_28aac10409666bbccf859a9b8804";

async function testRemoteConnection() {
  console.log("🚀 Testing Remote Browser Connection");

  try {
    // Initialize Hyperbrowser session
    console.log("🌐 Initializing Hyperbrowser session...");
    const hyperBrowser = new Hyperbrowser({
      apiKey: HYPERBROWSER_API_KEY,
      timeout: 60000,
    });
    
    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });
    
    const wsEndpoint = session.wsEndpoint;
    console.log("✅ Hyperbrowser session created");
    console.log("🔗 WebSocket endpoint:", wsEndpoint);

    // Create CDP connection
    const cdpInstance = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    // Get list of targets first
    console.log("📋 Getting list of targets...");
    const targets = await cdpInstance.Target.getTargets();
    console.log("🎯 Available targets:", targets.targetInfos.length);
    
    // Find a page target
    const pageTarget = targets.targetInfos.find(target => target.type === 'page');
    if (!pageTarget) {
      throw new Error("No page target found");
    }
    
    console.log("📍 Found page target:", pageTarget.targetId);
    
    // Attach to the target
    const attachResult = await cdpInstance.Target.attachToTarget({
      targetId: pageTarget.targetId,
      flatten: true
    });
    
    const sessionId = attachResult.sessionId;
    console.log("✅ Attached to target with sessionId:", sessionId);

    // Enable domains with sessionId
    await cdpInstance.Runtime.enable(null, sessionId);
    console.log("✅ Runtime domain enabled");

    await cdpInstance.Page.enable(null, sessionId);
    console.log("✅ Page domain enabled");

    // Navigate to Google
    const url = "https://accounts.google.com";
    console.log(`📍 Navigating to: ${url}`);
    await cdpInstance.Page.navigate({ url }, sessionId);

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Get page title
    const titleResult = await cdpInstance.Runtime.evaluate({
      expression: "document.title",
      returnByValue: true,
    }, sessionId);
    console.log("📄 Page title:", titleResult.result.value);

    // Get current URL
    const urlResult = await cdpInstance.Runtime.evaluate({
      expression: "window.location.href",
      returnByValue: true,
    }, sessionId);
    console.log("📍 Current URL:", urlResult.result.value);

    console.log("🎉 Remote browser connection test successful!");
    
  } catch (error) {
    console.error("❌ Error during remote browser test:", error);
  }
}

// Run the test
testRemoteConnection().catch(console.error);
