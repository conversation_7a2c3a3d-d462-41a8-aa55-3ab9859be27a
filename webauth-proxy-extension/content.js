/**
 * WebAuthentication Proxy - Content Script
 * 
 * This script intercepts WebAuthn requests in the remote browser
 * and forwards them to the local device for authentication.
 */

console.log('🔑 WebAuthentication Proxy content script loaded');

// Inject the main script into the page context
const script = document.createElement('script');
script.src = chrome.runtime.getURL('injected.js');
script.onload = function() {
  this.remove();
};
(document.head || document.documentElement).appendChild(script);

// Listen for messages from the injected script
window.addEventListener('message', async (event) => {
  if (event.source !== window) return;
  
  if (event.data.type === 'WEBAUTHN_REQUEST') {
    console.log('🔑 WebAuthn request received in content script:', event.data);
    
    try {
      // Forward the request to the background script
      const response = await chrome.runtime.sendMessage({
        type: 'WEBAUTHN_REQUEST',
        options: event.data.options,
        origin: window.location.origin
      });
      
      // Send response back to injected script
      window.postMessage({
        type: 'WEBAUTHN_RESPONSE',
        requestId: event.data.requestId,
        success: response.success,
        credential: response.credential,
        error: response.error
      }, '*');
      
    } catch (error) {
      console.error('❌ Error forwarding WebAuthn request:', error);
      
      // Send error response back to injected script
      window.postMessage({
        type: 'WEBAUTHN_RESPONSE',
        requestId: event.data.requestId,
        success: false,
        error: error.message
      }, '*');
    }
  }
});

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'WEBAUTHN_RESPONSE') {
    // Forward response to injected script
    window.postMessage({
      type: 'WEBAUTHN_RESPONSE',
      requestId: message.requestId,
      success: message.success,
      credential: message.credential,
      error: message.error
    }, '*');
  }
});

console.log('✅ WebAuthentication Proxy content script initialized');
