<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      text-align: center;
    }
    .status.active {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.inactive {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .info {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
    .button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      width: 100%;
      margin-top: 10px;
    }
    .button:hover {
      background-color: #0056b3;
    }
    .logs {
      max-height: 150px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 11px;
      margin-top: 15px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>🔑 WebAuth Proxy</h3>
  </div>
  
  <div id="status" class="status active">
    ✅ Extension Active
  </div>
  
  <div class="info">
    <p><strong>What this does:</strong></p>
    <ul>
      <li>Intercepts WebAuthn requests in remote browsers</li>
      <li>Forwards passkey authentication to your local device</li>
      <li>Enables Touch ID/Face ID in cloud browsers</li>
    </ul>
    
    <p><strong>Supported sites:</strong></p>
    <ul>
      <li>Google Accounts</li>
      <li>GitHub</li>
      <li>Microsoft</li>
      <li>And more...</li>
    </ul>
  </div>
  
  <button id="testButton" class="button">Test WebAuthn</button>
  <button id="clearLogs" class="button">Clear Logs</button>
  
  <div id="logs" class="logs">
    <div>Extension loaded and ready...</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
