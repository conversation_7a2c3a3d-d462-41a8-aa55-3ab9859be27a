/**
 * WebAuthentication Proxy - Background Script
 * 
 * This script handles WebAuthn requests from content scripts and
 * communicates with the local WebAuthn proxy server.
 */

console.log('🔑 WebAuthentication Proxy background script loaded');

// Configuration
const LOCAL_PROXY_URL = 'http://localhost:8080';
const PROXY_ENDPOINTS = {
  get: '/webauthn/get',
  create: '/webauthn/create'
};

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'WEBAUTHN_REQUEST') {
    console.log('🔑 WebAuthn request received in background:', message);
    
    // Handle the request asynchronously
    handleWebAuthnRequest(message, sender)
      .then(response => {
        console.log('✅ WebAuthn request completed:', response);
        sendResponse(response);
      })
      .catch(error => {
        console.error('❌ WebAuthn request failed:', error);
        sendResponse({
          success: false,
          error: error.message
        });
      });
    
    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

// Handle WebAuthn requests
async function handleWebAuthnRequest(message, sender) {
  try {
    const { options, origin } = message;
    const method = options.publicKey ? 'get' : 'create';
    
    console.log(`🔑 Processing WebAuthn ${method} request for origin:`, origin);
    
    // Try to forward to local proxy server first
    try {
      const response = await forwardToLocalProxy(method, options, origin);
      return response;
    } catch (proxyError) {
      console.log('⚠️ Local proxy not available, falling back to browser WebAuthn');
      
      // Fallback: try to use the browser's native WebAuthn
      return await fallbackToBrowserWebAuthn(method, options);
    }
    
  } catch (error) {
    console.error('❌ Error handling WebAuthn request:', error);
    throw error;
  }
}

// Forward request to local proxy server
async function forwardToLocalProxy(method, options, origin) {
  const endpoint = LOCAL_PROXY_URL + PROXY_ENDPOINTS[method];
  
  console.log(`🔗 Forwarding to local proxy: ${endpoint}`);
  
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      options: options,
      origin: origin,
      timestamp: Date.now()
    })
  });
  
  if (!response.ok) {
    throw new Error(`Local proxy error: ${response.status} ${response.statusText}`);
  }
  
  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || 'Local proxy request failed');
  }
  
  return {
    success: true,
    credential: result.credential
  };
}

// Fallback to browser's native WebAuthn
async function fallbackToBrowserWebAuthn(method, options) {
  console.log('🔄 Using browser fallback for WebAuthn');
  
  // Create a new tab for WebAuthn (required for some browsers)
  const tab = await chrome.tabs.create({
    url: 'data:text/html,<html><body><h1>WebAuthn Authentication</h1><p>Please complete the authentication...</p></body></html>',
    active: false
  });
  
  try {
    // Execute WebAuthn in the new tab
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: performWebAuthn,
      args: [method, options]
    });
    
    if (result && result[0] && result[0].result) {
      return {
        success: true,
        credential: result[0].result
      };
    } else {
      throw new Error('WebAuthn fallback failed');
    }
    
  } finally {
    // Clean up the tab
    try {
      await chrome.tabs.remove(tab.id);
    } catch (e) {
      console.log('Tab already closed');
    }
  }
}

// Function to be executed in the tab for WebAuthn
function performWebAuthn(method, options) {
  return new Promise(async (resolve, reject) => {
    try {
      let credential;
      
      if (method === 'get') {
        credential = await navigator.credentials.get(options);
      } else if (method === 'create') {
        credential = await navigator.credentials.create(options);
      } else {
        throw new Error('Unknown WebAuthn method: ' + method);
      }
      
      // Convert credential to serializable format
      const serializedCredential = {
        id: credential.id,
        type: credential.type,
        rawId: Array.from(new Uint8Array(credential.rawId)),
        response: {
          clientDataJSON: Array.from(new Uint8Array(credential.response.clientDataJSON)),
          authenticatorData: credential.response.authenticatorData ? 
            Array.from(new Uint8Array(credential.response.authenticatorData)) : undefined,
          signature: credential.response.signature ? 
            Array.from(new Uint8Array(credential.response.signature)) : undefined,
          userHandle: credential.response.userHandle ? 
            Array.from(new Uint8Array(credential.response.userHandle)) : undefined
        }
      };
      
      resolve(serializedCredential);
      
    } catch (error) {
      reject(error);
    }
  });
}

// Extension installation handler
chrome.runtime.onInstalled.addListener(() => {
  console.log('✅ WebAuthentication Proxy extension installed');
});

console.log('✅ WebAuthentication Proxy background script initialized');
