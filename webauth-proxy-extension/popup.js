/**
 * WebAuthentication Proxy - Popup Script
 */

document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const logsDiv = document.getElementById('logs');
  const testButton = document.getElementById('testButton');
  const clearLogsButton = document.getElementById('clearLogs');
  
  // Add log entry
  function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    logsDiv.appendChild(logEntry);
    logsDiv.scrollTop = logsDiv.scrollHeight;
  }
  
  // Test WebAuthn functionality
  testButton.addEventListener('click', async function() {
    addLog('Testing WebAuthn functionality...');
    
    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        addLog('❌ No active tab found');
        return;
      }
      
      addLog(`Testing on: ${tab.url}`);
      
      // Inject test script
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: testWebAuthnAvailability
      });
      
      if (results && results[0]) {
        const result = results[0].result;
        if (result.available) {
          addLog('✅ WebAuthn is available');
          addLog(`Platform authenticator: ${result.platformAuthenticator ? 'Yes' : 'No'}`);
          addLog(`Proxy active: ${result.proxyActive ? 'Yes' : 'No'}`);
        } else {
          addLog('❌ WebAuthn not available');
        }
      }
      
    } catch (error) {
      addLog(`❌ Test failed: ${error.message}`);
    }
  });
  
  // Clear logs
  clearLogsButton.addEventListener('click', function() {
    logsDiv.innerHTML = '<div>Logs cleared...</div>';
  });
  
  // Check extension status
  addLog('Extension popup opened');
  addLog('Checking WebAuthn proxy status...');
  
  // Listen for background script messages
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'LOG') {
      addLog(message.message);
    }
  });
});

// Function to test WebAuthn availability (injected into page)
function testWebAuthnAvailability() {
  const result = {
    available: false,
    platformAuthenticator: false,
    proxyActive: false
  };
  
  try {
    // Check if WebAuthn is available
    if (window.PublicKeyCredential) {
      result.available = true;
      
      // Check if platform authenticator is available
      if (PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable) {
        PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
          .then(available => {
            result.platformAuthenticator = available;
          });
      }
      
      // Check if our proxy is active
      if (document.getElementById('webauth-proxy-indicator')) {
        result.proxyActive = true;
      }
    }
  } catch (error) {
    console.error('WebAuthn test error:', error);
  }
  
  return result;
}
