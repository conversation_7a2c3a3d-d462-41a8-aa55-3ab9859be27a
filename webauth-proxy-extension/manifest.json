{"manifest_version": 3, "name": "WebAuthentication Proxy", "version": "1.0", "description": "Proxy WebAuthn requests from remote browser to local device", "permissions": ["activeTab", "scripting", "storage", "webRequest"], "host_permissions": ["https://accounts.google.com/*", "https://*.google.com/*", "http://localhost:*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://accounts.google.com/*", "https://*.google.com/*"], "js": ["content.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["https://*.google.com/*"]}], "action": {"default_popup": "popup.html", "default_title": "WebAuthentication Proxy"}}