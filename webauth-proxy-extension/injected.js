/**
 * WebAuthentication Proxy - Injected Script
 * 
 * This script runs in the page context and intercepts navigator.credentials.get()
 * calls to proxy WebAuthn requests to the local device.
 */

(function() {
  'use strict';
  
  console.log('🔑 WebAuthentication Proxy injected script loaded');
  
  // Store original methods
  const originalGet = navigator.credentials.get.bind(navigator.credentials);
  const originalCreate = navigator.credentials.create.bind(navigator.credentials);
  
  // Request counter for tracking
  let requestCounter = 0;
  
  // Override navigator.credentials.get
  navigator.credentials.get = async function(options) {
    console.log('🔑 navigator.credentials.get intercepted:', options);
    
    // Check if this is a WebAuthn request
    if (options && options.publicKey) {
      console.log('🔑 WebAuthn get request detected, proxying to local device');
      
      const requestId = ++requestCounter;
      
      // Send request to content script
      window.postMessage({
        type: 'WEBAUTHN_REQUEST',
        requestId: requestId,
        method: 'get',
        options: options,
        origin: window.location.origin
      }, '*');
      
      // Wait for response
      return new Promise((resolve, reject) => {
        const handleResponse = (event) => {
          if (event.source !== window) return;
          
          if (event.data.type === 'WEBAUTHN_RESPONSE' && event.data.requestId === requestId) {
            window.removeEventListener('message', handleResponse);
            
            if (event.data.success) {
              console.log('✅ WebAuthn get request successful');
              resolve(event.data.credential);
            } else {
              console.error('❌ WebAuthn get request failed:', event.data.error);
              reject(new Error(event.data.error || 'WebAuthn request failed'));
            }
          }
        };
        
        window.addEventListener('message', handleResponse);
        
        // Timeout after 60 seconds
        setTimeout(() => {
          window.removeEventListener('message', handleResponse);
          reject(new Error('WebAuthn request timeout'));
        }, 60000);
      });
    }
    
    // For non-WebAuthn requests, use original method
    return originalGet(options);
  };
  
  // Override navigator.credentials.create (for registration)
  navigator.credentials.create = async function(options) {
    console.log('🔑 navigator.credentials.create intercepted:', options);
    
    // Check if this is a WebAuthn request
    if (options && options.publicKey) {
      console.log('🔑 WebAuthn create request detected, proxying to local device');
      
      const requestId = ++requestCounter;
      
      // Send request to content script
      window.postMessage({
        type: 'WEBAUTHN_REQUEST',
        requestId: requestId,
        method: 'create',
        options: options,
        origin: window.location.origin
      }, '*');
      
      // Wait for response
      return new Promise((resolve, reject) => {
        const handleResponse = (event) => {
          if (event.source !== window) return;
          
          if (event.data.type === 'WEBAUTHN_RESPONSE' && event.data.requestId === requestId) {
            window.removeEventListener('message', handleResponse);
            
            if (event.data.success) {
              console.log('✅ WebAuthn create request successful');
              resolve(event.data.credential);
            } else {
              console.error('❌ WebAuthn create request failed:', event.data.error);
              reject(new Error(event.data.error || 'WebAuthn request failed'));
            }
          }
        };
        
        window.addEventListener('message', handleResponse);
        
        // Timeout after 60 seconds
        setTimeout(() => {
          window.removeEventListener('message', handleResponse);
          reject(new Error('WebAuthn request timeout'));
        }, 60000);
      });
    }
    
    // For non-WebAuthn requests, use original method
    return originalCreate(options);
  };
  
  // Add visual indicator that proxy is active
  const indicator = document.createElement('div');
  indicator.id = 'webauth-proxy-indicator';
  indicator.innerHTML = '🔑 WebAuth Proxy Active';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #4CAF50;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: Arial, sans-serif;
    font-size: 12px;
    z-index: 10000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  `;
  
  // Add indicator when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      document.body.appendChild(indicator);
    });
  } else {
    document.body.appendChild(indicator);
  }
  
  // Remove indicator after 5 seconds
  setTimeout(() => {
    if (indicator.parentNode) {
      indicator.parentNode.removeChild(indicator);
    }
  }, 5000);
  
  console.log('✅ WebAuthentication Proxy injected and ready');
  
})();
