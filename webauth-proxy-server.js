/**
 * WebAuthentication Proxy Server
 *
 * This server runs locally and handles WebAuthn requests from remote browsers,
 * performing the actual authentication on the local device.
 */

import express from "express";
import cors from "cors";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 8080;

// Middleware
app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());
app.use(express.static(join(__dirname, "public")));

// Store for pending requests
const pendingRequests = new Map();

// WebAuthn endpoints
app.post("/webauthn/get", async (req, res) => {
  console.log("🔑 WebAuthn get request received:", req.body);

  try {
    const { options, origin } = req.body;
    const requestId = Date.now().toString();

    // Store the request
    pendingRequests.set(requestId, {
      requestId,
      type: "get",
      options,
      origin,
      timestamp: Date.now(),
      resolve: null,
      reject: null,
    });

    // Create a promise that will be resolved when the user completes authentication
    const authPromise = new Promise((resolve, reject) => {
      const request = pendingRequests.get(requestId);
      request.resolve = resolve;
      request.reject = reject;

      // Timeout after 60 seconds
      setTimeout(() => {
        if (pendingRequests.has(requestId)) {
          pendingRequests.delete(requestId);
          reject(new Error("Authentication timeout"));
        }
      }, 60000);
    });

    // Broadcast the request to connected clients
    broadcastToClients({
      type: "WEBAUTHN_REQUEST",
      requestId,
      method: "get",
      options,
      origin,
    });

    // Wait for authentication result
    const credential = await authPromise;

    res.json({
      success: true,
      credential,
    });
  } catch (error) {
    console.error("❌ WebAuthn get error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

app.post("/webauthn/create", async (req, res) => {
  console.log("🔑 WebAuthn create request received:", req.body);

  try {
    const { options, origin } = req.body;
    const requestId = Date.now().toString();

    // Store the request
    pendingRequests.set(requestId, {
      requestId,
      type: "create",
      options,
      origin,
      timestamp: Date.now(),
      resolve: null,
      reject: null,
    });

    // Create a promise that will be resolved when the user completes authentication
    const authPromise = new Promise((resolve, reject) => {
      const request = pendingRequests.get(requestId);
      request.resolve = resolve;
      request.reject = reject;

      // Timeout after 60 seconds
      setTimeout(() => {
        if (pendingRequests.has(requestId)) {
          pendingRequests.delete(requestId);
          reject(new Error("Authentication timeout"));
        }
      }, 60000);
    });

    // Broadcast the request to connected clients
    broadcastToClients({
      type: "WEBAUTHN_REQUEST",
      requestId,
      method: "create",
      options,
      origin,
    });

    // Wait for authentication result
    const credential = await authPromise;

    res.json({
      success: true,
      credential,
    });
  } catch (error) {
    console.error("❌ WebAuthn create error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Handle authentication responses
app.post("/webauthn/response", (req, res) => {
  console.log("🔑 WebAuthn response received:", req.body);

  try {
    const { requestId, success, credential, error } = req.body;

    const request = pendingRequests.get(requestId);
    if (!request) {
      return res.status(404).json({
        success: false,
        error: "Request not found",
      });
    }

    // Remove from pending requests
    pendingRequests.delete(requestId);

    // Resolve or reject the promise
    if (success) {
      request.resolve(credential);
    } else {
      request.reject(new Error(error || "Authentication failed"));
    }

    res.json({ success: true });
  } catch (error) {
    console.error("❌ WebAuthn response error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Status endpoint
app.get("/status", (req, res) => {
  res.json({
    status: "running",
    pendingRequests: pendingRequests.size,
    connectedClients: connectedClients.size,
    timestamp: Date.now(),
  });
});

// WebSocket-like functionality using Server-Sent Events
const connectedClients = new Set();

app.get("/events", (req, res) => {
  // Set headers for Server-Sent Events
  res.writeHead(200, {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Cache-Control",
  });

  // Add client to connected clients
  connectedClients.add(res);
  console.log(`📡 Client connected. Total clients: ${connectedClients.size}`);

  // Send initial connection message
  res.write(
    `data: ${JSON.stringify({
      type: "CONNECTED",
      message: "Connected to WebAuthn proxy server",
    })}\n\n`
  );

  // Handle client disconnect
  req.on("close", () => {
    connectedClients.delete(res);
    console.log(
      `📡 Client disconnected. Total clients: ${connectedClients.size}`
    );
  });
});

// Broadcast message to all connected clients
function broadcastToClients(message) {
  const data = `data: ${JSON.stringify(message)}\n\n`;

  connectedClients.forEach((client) => {
    try {
      client.write(data);
    } catch (error) {
      console.error("Error broadcasting to client:", error);
      connectedClients.delete(client);
    }
  });

  console.log(`📡 Broadcasted message to ${connectedClients.size} clients`);
}

// Serve the authentication UI
app.get("/", (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>WebAuthn Proxy Server</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { padding: 15px; border-radius: 8px; margin: 20px 0; }
        .status.running { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .request { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        .logs { background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
      </style>
    </head>
    <body>
      <h1>🔑 WebAuthn Proxy Server</h1>
      
      <div class="status running">
        ✅ Server is running on port ${PORT}
      </div>
      
      <div>
        <h3>📊 Status</h3>
        <p>Pending requests: <span id="pendingCount">0</span></p>
        <p>Connected clients: <span id="clientCount">0</span></p>
        <p>Last update: <span id="lastUpdate">-</span></p>
      </div>
      
      <div>
        <h3>📋 Pending Authentication Requests</h3>
        <div id="requests">No pending requests</div>
      </div>
      
      <div>
        <h3>📝 Logs</h3>
        <div id="logs" class="logs">Server started...\n</div>
        <button onclick="clearLogs()" class="button">Clear Logs</button>
      </div>
      
      <script>
        const eventSource = new EventSource('/events');
        const requestsDiv = document.getElementById('requests');
        const logsDiv = document.getElementById('logs');
        
        eventSource.onmessage = function(event) {
          const data = JSON.parse(event.data);
          addLog('Received: ' + data.type);
          
          if (data.type === 'WEBAUTHN_REQUEST') {
            showAuthRequest(data);
          }
        };
        
        function showAuthRequest(data) {
          // Store the request data for later use
          currentRequests.set(data.requestId, data);

          const requestDiv = document.createElement('div');
          requestDiv.className = 'request';
          requestDiv.innerHTML = \`
            <h4>🔑 Authentication Request</h4>
            <p><strong>Method:</strong> \${data.method}</p>
            <p><strong>Origin:</strong> \${data.origin}</p>
            <p><strong>Request ID:</strong> \${data.requestId}</p>
            <button onclick="handleAuth('\${data.requestId}', '\${data.method}', true)" class="button">✅ Authenticate</button>
            <button onclick="handleAuth('\${data.requestId}', '\${data.method}', false)" class="button danger">❌ Cancel</button>
          \`;
          requestsDiv.appendChild(requestDiv);
        }
        
        // Store current requests on client side
        let currentRequests = new Map();

        // Convert WebAuthn options from JSON back to proper format
        function convertWebAuthnOptions(options) {
          const converted = { ...options };
      console.log('Original options:', JSON.stringify(options, null, 2));

          // Convert challenge from object back to Uint8Array
          if (options.challenge && typeof options.challenge === 'object') {
            // If challenge is empty object, create a random challenge
            if (Object.keys(options.challenge).length === 0) {
              converted.challenge = new Uint8Array(32);
              crypto.getRandomValues(converted.challenge);
            } else {
              // Convert from object with numeric keys back to Uint8Array
              const challengeArray = Object.values(options.challenge);
              converted.challenge = new Uint8Array(challengeArray);
            }
          }

          // Convert allowCredentials IDs from objects back to Uint8Arrays
          if (options.allowCredentials && Array.isArray(options.allowCredentials)) {
            converted.allowCredentials = options.allowCredentials.map(cred => ({
              ...cred,
              id: cred.id && typeof cred.id === 'object' ?
                new Uint8Array(Object.values(cred.id)) : cred.id
            }));
          }

          return converted;
        }

        async function handleAuth(requestId, method, success) {
          if (success) {
            try {
              // Get the request data from client-side storage
              const requestData = currentRequests.get(requestId);
              if (!requestData) {
                throw new Error('Request data not found');
              }

              addLog('🔑 Performing local ' + method + ' authentication with TouchID/FaceID...');

              // Perform actual WebAuthn authentication using the original options
              let credential;
              if (method === 'get') {
                // Convert the options to proper format for WebAuthn
                const webAuthnOptions = convertWebAuthnOptions(requestData.options.publicKey);

                credential = await navigator.credentials.get({
                  publicKey: webAuthnOptions
                });

                addLog('✅ TouchID/FaceID authentication successful');
              } else if (method === 'create') {
                // Convert the options to proper format for WebAuthn
                const webAuthnOptions = convertWebAuthnOptions(requestData.options.publicKey);

                credential = await navigator.credentials.create({
                  publicKey: webAuthnOptions
                });

                addLog('✅ TouchID/FaceID registration successful');
              } else {
                throw new Error('Unsupported method: ' + method);
              }

              // Send response back to server
              await fetch('/webauthn/response', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  requestId,
                  success: true,
                  credential: credential
                })
              });

              addLog('✅ Authentication response sent to remote browser');
              
            } catch (error) {
              addLog('❌ Authentication failed: ' + error.message);
              
              await fetch('/webauthn/response', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  requestId,
                  success: false,
                  error: error.message
                })
              });
            }
          } else {
            await fetch('/webauthn/response', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                requestId,
                success: false,
                error: 'User cancelled'
              })
            });
            
            addLog('❌ Authentication cancelled');
          }
          
          // Remove the request div
          // event.target.closest('.request').remove();
        }
        
        function addLog(message) {
          const timestamp = new Date().toLocaleTimeString();
          logsDiv.textContent += \`[\${timestamp}] \${message}\\n\`;
          logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
          logsDiv.textContent = 'Logs cleared...\\n';
        }
        
        // Update status periodically
        setInterval(async () => {
          try {
            const response = await fetch('/status');
            const status = await response.json();
            document.getElementById('pendingCount').textContent = status.pendingRequests;
            document.getElementById('clientCount').textContent = status.connectedClients;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
          } catch (error) {
            console.error('Failed to update status:', error);
          }
        }, 2000);
      </script>
    </body>
    </html>
  `);
});

// Start server
app.listen(PORT, () => {
  console.log(
    `🚀 WebAuthentication Proxy Server running on http://localhost:${PORT}`
  );
  console.log(
    `📡 Server-Sent Events endpoint: http://localhost:${PORT}/events`
  );
  console.log(`🔑 WebAuthn endpoints:`);
  console.log(`   POST http://localhost:${PORT}/webauthn/get`);
  console.log(`   POST http://localhost:${PORT}/webauthn/create`);
  console.log(`📊 Status endpoint: http://localhost:${PORT}/status`);
});
