<!DOCTYPE html>
<html>
<head>
    <title>WebAuthn Proxy Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔑 WebAuthn Proxy Test</h1>
    <p>This page tests the WebAuth proxy functionality by making WebAuthn requests.</p>
    
    <button class="button" onclick="testWebAuthnGet()">🔑 Test WebAuthn Get (Passkey Login)</button>
    <button class="button" onclick="testWebAuthnCreate()">📝 Test WebAuthn Create (Passkey Registration)</button>
    <button class="button" onclick="clearLog()">🗑️ Clear Log</button>
    
    <div id="log" class="log">Ready to test WebAuthn proxy...\n</div>

    <script>
        // Install WebAuthenticationProxy (same as in google-login-remote.js)
        (function() {
          // WebAuthenticationProxy implementation
          if (!window.webAuthenticationProxy) {
            window.webAuthenticationProxy = {
              isEnabled: true,

              // Override navigator.credentials.get for passkey requests
              originalGet: navigator.credentials.get.bind(navigator.credentials),
              originalCreate: navigator.credentials.create.bind(navigator.credentials),

              async handlePasskeyRequest(options, method = 'get') {
                log('🔑 Passkey request intercepted: ' + method);
                log('Options: ' + JSON.stringify(options, null, 2));

                try {
                  // Send request to local proxy server
                  const response = await fetch('http://localhost:8080/webauthn/' + method, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      options: options,
                      origin: window.location.origin,
                      timestamp: Date.now()
                    })
                  });

                  if (!response.ok) {
                    throw new Error('Local proxy server not available. Please start the proxy server on your local machine.');
                  }

                  const result = await response.json();

                  if (!result.success) {
                    throw new Error(result.error || 'Authentication failed');
                  }

                  log('✅ Passkey authentication successful via local proxy');
                  return result.credential;

                } catch (error) {
                  log('❌ Local proxy authentication failed: ' + error.message);
                  throw error;
                }
              }
            };

            // Override navigator.credentials.get
            navigator.credentials.get = async function(options) {
              if (options.publicKey) {
                log('🔑 Intercepting passkey GET request');
                return window.webAuthenticationProxy.handlePasskeyRequest(options, 'get');
              }
              return window.webAuthenticationProxy.originalGet(options);
            };

            // Override navigator.credentials.create
            navigator.credentials.create = async function(options) {
              if (options.publicKey) {
                log('🔑 Intercepting passkey CREATE request');
                return window.webAuthenticationProxy.handlePasskeyRequest(options, 'create');
              }
              return window.webAuthenticationProxy.originalCreate(options);
            };

            log('✅ WebAuthenticationProxy installed');
          }
        })();

        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
        }

        async function testWebAuthnGet() {
            log('🔑 Testing WebAuthn GET (passkey login)...');
            
            try {
                const credential = await navigator.credentials.get({
                    publicKey: {
                        challenge: new Uint8Array(32),
                        timeout: 60000,
                        rpId: window.location.hostname,
                        userVerification: 'preferred',
                        allowCredentials: []
                    }
                });
                
                log('✅ WebAuthn GET successful!');
                log('Credential: ' + JSON.stringify(credential, null, 2));
            } catch (error) {
                log('❌ WebAuthn GET failed: ' + error.message);
            }
        }

        async function testWebAuthnCreate() {
            log('📝 Testing WebAuthn CREATE (passkey registration)...');
            
            try {
                const credential = await navigator.credentials.create({
                    publicKey: {
                        challenge: new Uint8Array(32),
                        rp: {
                            name: "WebAuthn Proxy Test",
                            id: window.location.hostname,
                        },
                        user: {
                            id: new Uint8Array(16),
                            name: "<EMAIL>",
                            displayName: "Test User",
                        },
                        pubKeyCredParams: [{alg: -7, type: "public-key"}],
                        authenticatorSelection: {
                            authenticatorAttachment: "platform",
                            userVerification: "preferred"
                        },
                        timeout: 60000,
                    }
                });
                
                log('✅ WebAuthn CREATE successful!');
                log('Credential: ' + JSON.stringify(credential, null, 2));
            } catch (error) {
                log('❌ WebAuthn CREATE failed: ' + error.message);
            }
        }

        // Fill challenge with random data
        crypto.getRandomValues(new Uint8Array(32));
        
        log('🚀 WebAuthn Proxy Test page loaded');
        log('📡 Proxy server should be running at http://localhost:8080');
    </script>
</body>
</html>
