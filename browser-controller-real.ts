import { CDP } from "./simple-cdp";

declare global {
  interface Window {
    browserController: any;
  }
}

(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log("[browserController]", ...args);
    }
  }

  function error(...args: any[]) {
    console.error("[browserController]", ...args);
  }

  /**
   * Initializes the browser controller with its own CDP session
   * Similar to puppeteer.connect(wsEndpoint) - creates own session context
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string
  ): Promise<void> {
    log("Connecting to CDP and attaching to target:", targetId);
    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log("Browser controller initialized with sessionId:", sessionId);
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   * Mimics puppeteer.connect() behavior by connecting to the existing page target
   * @param wsEndpoint - WebSocket endpoint
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log("Attaching to target:", targetId, wsEndpoint);

      // Attach to the existing page target
      const { sessionId: attachedSessionId } =
        await cdpClient.Target.attachToTarget({
          targetId: targetId,
          flatten: true,
        });

      sessionId = attachedSessionId;
      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      log(
        "✓ Browser controller attached to target",
        targetId,
        "with sessionId:",
        sessionId
      );
    } catch (err) {
      error("Failed to connect to CDP and attach to target:", err);
      throw err;
    }
  }

  /**
   * Takes a screenshot of the current page using CDP
   * @param options - Screenshot options
   * @returns Promise with screenshot data
   */
  async function takeScreenshot(
    options: {
      format?: "png" | "jpeg";
      quality?: number;
      clip?: {
        x: number;
        y: number;
        width: number;
        height: number;
        scale: number;
      };
      fromSurface?: boolean;
      captureBeyondViewport?: boolean;
    } = {}
  ): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      if (!cdpClient || !sessionId) {
        throw new Error("CDP client not initialized. Call init() first.");
      }

      log("Taking screenshot with options:", options);

      const screenshotOptions: any = {
        format: options.format || "png",
        quality: options.quality || 100,
        fromSurface: options.fromSurface !== false,
        captureBeyondViewport: options.captureBeyondViewport !== false,
      };

      // Add clip if provided
      if (options.clip) {
        screenshotOptions.clip = {
          x: options.clip.x,
          y: options.clip.y,
          width: options.clip.width,
          height: options.clip.height,
          scale: options.clip.scale || 1,
        };
      }

      const result = await cdpClient.Page.captureScreenshot(
        screenshotOptions,
        sessionId
      );

      log(
        "Screenshot captured successfully, data length:",
        result.data?.length || 0
      );

      return {
        success: true,
        data: result.data,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      error("Failed to take screenshot:", errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Gets page information using CDP
   * @returns Promise with page info
   */
  async function getPageInfo(): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      if (!cdpClient || !sessionId) {
        throw new Error("CDP client not initialized. Call init() first.");
      }

      log("Getting page info...");

      // Get page title and URL
      const result = await cdpClient.Runtime.evaluate(
        {
          expression: `JSON.stringify({
          title: document.title,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })`,
        },
        sessionId
      );

      const pageInfo = JSON.parse(result.result.value);

      log("Page info retrieved:", pageInfo);

      return {
        success: true,
        data: pageInfo,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      error("Failed to get page info:", errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Gets browser controller status
   * @returns Status information
   */
  function getStatus(): { success: boolean; data: any } {
    return {
      success: true,
      data: {
        connected: !!cdpClient,
        sessionId: sessionId,
        timestamp: Date.now(),
      },
    };
  }

  // Expose public API
  (globalThis as any).browserController = {
    init,
    takeScreenshot,
    getPageInfo,
    getStatus,
  };
})();
