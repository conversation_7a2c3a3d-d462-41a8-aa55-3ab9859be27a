/**
 * Simple CDP Script - Navigate to Kazeel Login Page
 *
 * This script uses simple-cdp to navigate to dev-tasks.kazeel.com/auth/login
 *
 * Prerequisites:
 * - Start Chrome with: chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 * - Or use: google-chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 */

import { createTarget, CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function navigateToKazeelLogin() {
  // Navigate to Kazeel login page
  const url =
    "https://dev-tasks.kazeel.com/auth/captcha?email=dirtynoobs%40yahoo.com&captchaText=2E9IX&captchaAnswer=2E9IX";
  console.log(`📍 Creating target for: ${url}`);
  const targetInfo = await createTarget(url);
  console.log("✅ Target created:", targetInfo);

  // Create a CDP instance for the target
  const cdpInstance = new CDP(targetInfo);

  // Enable necessary domains
  await cdpInstance.Runtime.enable();
  console.log("✅ Runtime domain enabled");

  await cdpInstance.Page.enable();
  console.log("✅ Page domain enabled");

  // Wait for page to load
  console.log("⏳ Waiting for page to load...");
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Get page title
  const titleResult = await cdpInstance.Runtime.evaluate({
    expression: "document.title",
  });
  console.log("📄 Page title:", titleResult.result.value);

  // Get current URL to confirm navigation
  const urlResult = await cdpInstance.Runtime.evaluate({
    expression: "window.location.href",
  });
  console.log("🌐 Current URL:", urlResult.result.value);

  const window = await cdpInstance.Browser.getWindowForTarget({
    targetId: targetInfo.targetId,
  });

  // setviewport to 1024x768
  await cdpInstance.Emulation.setDeviceMetricsOverride({
    width: 1024,
    height: 768,
    deviceScaleFactor: 1,
    mobile: false,
  });

  console.log("Focused window ID:", window.windowId);

  await cdpInstance.Runtime.evaluate({
    expression: `
      (async function() {
        console.log("🎥 Requesting screen sharing...");
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            frameRate: 22,
            width: window.width,
            height: window.height,
          },
          preferCurrentTab: true,
        });
        window.screenStream = screenStream;
        console.log("✅ Screen sharing started successfully");
      })();
    `,
  });

  const viewport = {
    width: 1024,
    height: 768,
  };
  // setviewport to 1024x768
  await cdpInstance.Emulation.setDeviceMetricsOverride({
    ...viewport,
    deviceScaleFactor: 1,
    mobile: false,
  });
  const CHROME_HEADER = 88;
  const CHROME_SCREEN_SHARING_BANNER = 55;
  await cdpInstance.Browser.setWindowBounds({
    windowId: window.windowId,
    bounds: {
      left: 0,
      top: 0,
      width: viewport.width,
      height: viewport.height + CHROME_HEADER + CHROME_SCREEN_SHARING_BANNER,
    },
  });

  const layoutMetrics = await cdpInstance.Page.getLayoutMetrics(undefined);
  const viewportX = {
    width: layoutMetrics.cssLayoutViewport.clientWidth,
    height: layoutMetrics.cssLayoutViewport.clientHeight,
  };
  console.log({ viewportX });
  //  add trailing mouse movement tracker. Inject a script that listens for mouse move and draws a dot at the current mouse position
  await cdpInstance.Runtime.evaluate({
    expression: `
      function trackMouse() {
        const dot = document.createElement('div');
        dot.style.position = 'absolute';
        dot.style.width = '10px';
        dot.style.height = '10px';
        dot.style.borderRadius = '50%';
        dot.style.backgroundColor = 'red';
        dot.style.pointerEvents = 'none';
        document.body.appendChild(dot);
        
        function updateDot(event) {
          dot.style.left = event.clientX - 5 + 'px';
          dot.style.top = event.clientY - 5 + 'px';
        }
        
        document.addEventListener('mousemove', updateDot);
      }
      
      trackMouse();
    `,
  });

  // await cdpInstance.Emulation.setDeviceMetricsOverride({
  //   width: viewport.width,
  //   height: viewport.height,

  //   deviceScaleFactor: 1,
  //   mobile: false,
  // });
}

// Run the script
navigateToKazeelLogin();
