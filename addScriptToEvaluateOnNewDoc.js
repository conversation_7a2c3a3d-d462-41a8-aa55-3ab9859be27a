import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

/**
 * Test script to experiment with Chrome DevTools Protocol's addScriptToEvaluateOnNewDocument
 * session persistence behavior.
 *
 * Research Question: Does addScriptToEvaluateOnNewDocument persist at the browser target level
 * across different CDP sessions, or is it tied to the specific CDP session that registered it?
 */

// Test script to inject - sets a global variable and logs to console
const TEST_SCRIPT = `
  console.log('🚀 TEST SCRIPT EXECUTED! Session persistence test active.');
  window.testScriptExecuted = true;
  window.testScriptTimestamp = Date.now();
  window.testScriptExecutionCount = (window.testScriptExecutionCount || 0) + 1;
`;

// Helper function to wait for a specified time
function wait(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Helper function to check if test script executed
async function checkScriptExecution(cdp, sessionId, context = "unknown") {
  try {
    const result = await cdp.Runtime.evaluate(
      {
        expression: `({
        executed: !!window.testScriptExecuted,
        timestamp: window.testScriptTimestamp || null,
        executionCount: window.testScriptExecutionCount || 0,
        currentTime: Date.now()
      })`,
      },
      sessionId
    );

    const data = result.result.value;
    console.log(`📊 Script execution check [${context}]:`, {
      executed: data.executed,
      timestamp: data.timestamp ? new Date(data.timestamp).toISOString() : null,
      executionCount: data.executionCount,
      timeSinceExecution: data.timestamp
        ? data.currentTime - data.timestamp
        : null,
    });

    return data.executed;
  } catch (error) {
    console.error(
      `❌ Failed to check script execution [${context}]:`,
      error.message
    );
    return false;
  }
}

async function sessionPersistenceTest() {
  console.log(
    "\n🧪 === CDP addScriptToEvaluateOnNewDocument Session Persistence Test ===\n"
  );

  let targetId = null;

  try {
    // ========================================
    // PHASE 1: First CDP Session Setup
    // ========================================
    console.log("📋 PHASE 1: Setting up first CDP session...");

    const cdp1 = new CDP({
      webSocketDebuggerUrl:
        "ws://localhost:9222/devtools/browser/62ac528e-3152-4a9c-a037-1189ec702d5f",
    });

    // Create a new target
    console.log("🎯 Creating new browser target...");
    const target = await cdp1.Target.createTarget({
      url: "about:blank",
    });
    targetId = target.targetId;
    console.log(`✅ Target created with ID: ${targetId}`);

    // Attach to the target
    const { sessionId: session1Id } = await cdp1.Target.attachToTarget({
      targetId: targetId,
      flatten: true,
    });
    console.log(`🔗 Attached to target with session ID: ${session1Id}`);

    // Enable necessary domains
    await cdp1.Runtime.enable(null, session1Id);
    await cdp1.Page.enable(null, session1Id);
    await cdp1.Console.enable(null, session1Id);
    console.log("✅ Enabled Runtime, Page, and Console domains");

    // Set up console event listener to catch our test script logs
    cdp1.Runtime.addEventListener("consoleAPICalled", (event) => {
      if (event.sessionId === session1Id) {
        console.log({ event });
      }
    });

    // ========================================
    // PHASE 2: Inject Script in First Session
    // ========================================
    console.log(
      "\n📋 PHASE 2: Injecting script via addScriptToEvaluateOnNewDocument..."
    );

    const { identifier } = await cdp1.Page.addScriptToEvaluateOnNewDocument(
      {
        source: TEST_SCRIPT,
      },
      session1Id
    );
    console.log(`✅ Script injected with identifier: ${identifier}`);

    // Navigate to test page to trigger script execution
    console.log("🌐 Navigating to test page (httpbin.org/html)...");
    await cdp1.Page.navigate(
      {
        url: "https://httpbin.org/html",
      },
      session1Id
    );

    // Wait for navigation and script execution
    await wait(3000);

    // Check if script executed in first session
    const executed1 = await checkScriptExecution(
      cdp1,
      session1Id,
      "Session 1 - First Navigation"
    );

    // Navigate to another page to test script persistence within same session
    console.log("🌐 Navigating to second page (httpbin.org/json)...");
    await cdp1.Page.navigate(
      {
        url: "https://httpbin.org/json",
      },
      session1Id
    );
    await wait(2000);

    const executed2 = await checkScriptExecution(
      cdp1,
      session1Id,
      "Session 1 - Second Navigation"
    );

    // ========================================
    // PHASE 3: Close First Session
    // ========================================
    console.log("\n📋 PHASE 3: Closing first CDP session...");

    // Detach from target (closes the session)
    await cdp1.Target.detachFromTarget({ sessionId: session1Id });
    console.log("🔌 Detached from target - Session 1 closed");

    // Reset CDP connection
    cdp1.reset();
    console.log("🔄 CDP connection reset");

    await wait(1000);

    // ========================================
    // PHASE 4: Second CDP Session (New Connection)
    // ========================================
    console.log("\n📋 PHASE 4: Creating new CDP session to same target...");

    const cdp2 = new CDP({
      webSocketDebuggerUrl:
        "ws://localhost:9222/devtools/browser/62ac528e-3152-4a9c-a037-1189ec702d5f",
    });

    // Attach to the SAME target with new session
    const { sessionId: session2Id } = await cdp2.Target.attachToTarget({
      targetId: targetId,
      flatten: true,
    });
    console.log(
      `🔗 Attached to same target with NEW session ID: ${session2Id}`
    );

    // Enable necessary domains for new session
    await cdp2.Runtime.enable(null, session2Id);
    await cdp2.Page.enable(null, session2Id);
    await cdp2.Console.enable(null, session2Id);
    console.log("✅ Enabled domains for new session");

    // Set up console event listener for second session
    cdp2.Runtime.addEventListener("consoleAPICalled", (event) => {
      console.log({ event });
    });

    // ========================================
    // PHASE 5: Test Script Persistence
    // ========================================
    console.log("\n📋 PHASE 5: Testing script persistence across sessions...");
    console.log(
      "❗ CRITICAL TEST: Navigating WITHOUT calling addScriptToEvaluateOnNewDocument again"
    );

    // Navigate to a new page WITHOUT calling addScriptToEvaluateOnNewDocument
    await cdp2.Page.navigate(
      {
        url: "https://httpbin.org/uuid",
      },
      session2Id
    );
    await wait(3000);

    // Check if the previously injected script still executes
    const persistedExecution = await checkScriptExecution(
      cdp2,
      session2Id,
      "Session 2 - Persistence Test"
    );

    // Navigate to another page for additional confirmation
    console.log("🌐 Second navigation test in new session...");
    await cdp2.Page.navigate(
      {
        url: "https://httpbin.org/ip",
      },
      session2Id
    );
    await wait(2000);

    const persistedExecution2 = await checkScriptExecution(
      cdp2,
      session2Id,
      "Session 2 - Second Navigation"
    );

    // ========================================
    // PHASE 6: Results Analysis
    // ========================================
    console.log("\n📋 PHASE 6: Results Analysis");
    console.log("=" * 60);

    console.log(
      `✅ Session 1 - First navigation script executed: ${executed1}`
    );
    console.log(
      `✅ Session 1 - Second navigation script executed: ${executed2}`
    );
    console.log(
      `🔍 Session 2 - First navigation script executed: ${persistedExecution}`
    );
    console.log(
      `🔍 Session 2 - Second navigation script executed: ${persistedExecution2}`
    );

    console.log("\n🎯 CONCLUSION:");
    if (persistedExecution || persistedExecution2) {
      console.log(
        "✅ SUCCESS: addScriptToEvaluateOnNewDocument PERSISTS at the TARGET LEVEL!"
      );
      console.log(
        "   Scripts registered in one CDP session continue to execute in new sessions"
      );
      console.log("   attached to the same browser target.");
    } else {
      console.log(
        "❌ RESULT: addScriptToEvaluateOnNewDocument is SESSION-SPECIFIC"
      );
      console.log(
        "   Scripts are tied to the CDP session that registered them and do not"
      );
      console.log("   persist when new sessions are created.");
    }

    // Cleanup
    console.log("\n🧹 Cleaning up...");
    await cdp2.Target.detachFromTarget({ sessionId: session2Id });
    await cdp2.Target.closeTarget({ targetId: targetId });
    cdp2.reset();
    console.log("✅ Cleanup completed");
  } catch (error) {
    console.error("❌ Test failed with error:", error);

    // Attempt cleanup on error
    if (targetId) {
      try {
        const cleanupCdp = new CDP({
          webSocketDebuggerUrl:
            "ws://localhost:9222/devtools/browser/62ac528e-3152-4a9c-a037-1189ec702d5f",
        });
        await cleanupCdp.Target.closeTarget({ targetId: targetId });
        cleanupCdp.reset();
        console.log("🧹 Emergency cleanup completed");
      } catch (cleanupError) {
        console.error("❌ Cleanup also failed:", cleanupError.message);
      }
    }
  }
}

async function main() {
  try {
    await sessionPersistenceTest();
  } catch (error) {
    console.error("❌ Main execution error:", error);
  }
}

// Run the test
main();
