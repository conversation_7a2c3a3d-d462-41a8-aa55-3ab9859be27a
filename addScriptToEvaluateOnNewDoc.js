import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;
async function advancedExample() {
  console.log("\n=== Advanced Example with Auto-Attach ===");

  try {
    let wsEndpoint =
      "ws://localhost:9223/devtools/browser/cca1a936-b1e7-45eb-8ef6-5d51654868d1";
    let cdp = new CDP({
      webSocketDebuggerUrl: `${wsEndpoint}&keepAlive=true`,
    });

    let cdp2 = new CDP({
      webSocketDebuggerUrl: `${wsEndpoint}&keepAlive=true`,
    });

    await cdp.Target.setAutoAttach({
      autoAttach: true,
      flatten: true,
      waitForDebuggerOnStart: false,
    });

    const target = await cdp.Target.createTarget({
      url: "https://facebook.com",
    });

    const { sessionId } = await cdp.Target.attachToTarget({
      targetId: target.targetId,
      flatten: true,
    });
    await cdp.Performance.enable(null, sessionId);
    await cdp.Page.enable(null, sessionId);
    await cdp.Runtime.enable(null, sessionId);
    await cdp.DOM.enable(null, sessionId);
    await cdp.Network.enable(null, sessionId);
    await cdp.Log.enable(null, sessionId);

    await cdp2.Page.navigate({ url: "https://facebook.com" }, sessionId);

    console.log("we have session id from cdp1", { sessionId });
  } catch (error) {
    console.error("Error in advanced example:", error);
  }
}

async function main() {
  try {
    await advancedExample();
  } catch (error) {
    console.error("Main execution error:", error);
  }
}
main();
