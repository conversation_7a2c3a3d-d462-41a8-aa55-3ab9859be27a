(()=>{var T="message",y="open",w="close",b="error",h="ConnectionRefused",v="ConnectionError";var E="GET",O="PUT",L="http://localhost:9222",N="json/version",U="json",A="json/new",I="json/activate",_="json/close";var S={apiUrl:L,apiPath:N,apiPathTargets:U,apiPathNewTarget:A,apiPathActivateTarget:I,apiPathCloseTarget:_,connectionMaxRetry:20,connectionRetryDelay:500},m=class{#t;#e=Object.assign({},l);#n=new Map;Runtime;Target;Page;Input;Network;DOM;Emulation;Debugger;Console;CSS;Profiler;HeapProfiler;Security;ServiceWorker;Storage;SystemInfo;Browser;Animation;Accessibility;constructor(e){return Object.assign(this.#e,e),["Runtime","Target","<PERSON>","Console","Network","Input","DOM","CSS","Debugger","Profiler","HeapProfiler","Security","ServiceWorker","Storage","SystemInfo","Browser","Emulation","Animation","Accessibility"].forEach(i=>{this[i]=this.createDomain(i)}),new Proxy(this,{get(i,n){return typeof n=="string"&&(n in i||(i[n]=i.createDomain(n))),i[n]}})}createDomain(e){let t=this;return new Proxy(Object.create(null),{get(n,s){if(typeof s=="string")return s==="addEventListener"?t.getDomainListenerFunction("addEventListener",e):s==="removeEventListener"?t.getDomainListenerFunction("removeEventListener",e):(n[s]||(n[s]=t.getDomainMethodFunction(s,e)),n[s])}})}getDomainMethodFunction(e,t){let i=this;return async(s={},c)=>{await i.ready();let o=i.#n.get(t);if(o!==void 0){for(;o.length>0;){let D=o.shift();D&&i.#t&&i.#t[D.methodName](`${D.domainName}.${D.type}`,D.listener)}i.#n.delete(t)}if(!i.#t)throw new Error("Connection not established");let d=s||{};return i.#t.sendMessage(`${t}.${e}`,d,c)}}getDomainListenerFunction(e,t){let i=this;return(n,s)=>{if(i.#t===void 0){let c=i.#n.get(t);c===void 0&&(c=[],i.#n.set(t,c)),c.push({methodName:e,domainName:t,type:n,listener:s})}else i.#t[e](`${t}.${n}`,s)}}async ready(){if(this.#t===void 0){let e=this.#e.webSocketDebuggerUrl;if(e===void 0){let i=new URL(this.#e.apiPath,this.#e.apiUrl);e=(await u(i,this.#e)).webSocketDebuggerUrl}let t=new p(e);await t.open(),this.#t=t}}get options(){return this.#e}set options(e){Object.assign(this.#e,e)}get connection(){if(!this.#t)throw new Error("Connection not established. Call a CDP method first to establish connection.");return this.#t}reset(){this.#t!==void 0&&(this.#t.close(),this.#t=void 0,this.#n.clear())}static getTargets(){let{apiPathTargets:e,apiUrl:t}=l;return u(new URL(e,t),l)}static createTarget(e){let{apiPathNewTarget:t,apiUrl:i}=l,n=e?`${t}?${e}`:t;return u(new URL(n,i),l,O)}static async activateTarget(e){let{apiPathActivateTarget:t,apiUrl:i}=l;await u(new URL(`${t}/${e}`,i),l,E,!1)}static async closeTarget(e){let{apiPathCloseTarget:t,apiUrl:i}=l;await u(new URL(`${t}/${e}`,i),l,E,!1)}},l=Object.assign({},S),M=new m(l),R=m.getTargets,x=m.createTarget,F=m.activateTarget,k=m.closeTarget;var p=class extends EventTarget{#t;#e;#n=new Map;#i=0;constructor(e){super(),this.#t=e}open(){return this.#e=new WebSocket(this.#t),this.#e.addEventListener(T,e=>this.#s(JSON.parse(e.data))),new Promise((e,t)=>{this.#e.addEventListener(y,()=>e()),this.#e.addEventListener(w,i=>t(new Error(i.reason))),this.#e.addEventListener(b,()=>t(new Error))})}sendMessage(e,t={},i){if(!this.#e)throw new Error("WebSocket not connected");let n=this.#i,s=JSON.stringify({id:n,method:e,params:t,sessionId:i});this.#i=(this.#i+1)%Number.MAX_SAFE_INTEGER,this.#e.send(s);let c,o=new Promise((d,D)=>c={resolve:d,reject:D,method:e,params:t,sessionId:i});return this.#n.set(n,c),o}close(){this.#e&&this.#e.close()}#s({id:e,method:t,result:i,error:n,params:s,sessionId:c}){if(e!==void 0){let o=this.#n.get(e);if(o){let{resolve:d,reject:D}=o;if(n===void 0)d(i);else{let C=n.message+` when calling ${o.method}(${JSON.stringify(o.params)})${o.sessionId===void 0?"":` (sessionId ${JSON.stringify(o.sessionId)})`}`,r=new Error(C);r.code=n.code,D(r)}this.#n.delete(e)}}if(t!==void 0){let o=new Event(t);o.params=s,o.sessionId=c,this.dispatchEvent(o)}}};function u(P,e,t=E,i=!0){return f(async()=>{let n;try{n=await fetch(P,{method:t})}catch(s){let c=s;throw c.code=h,c}if(n.status>=400){let s=new Error(n.statusText||`HTTP Error ${n.status}`);throw s.status=n.status,s.code=v,s}else return i?n.json():n.text()},e)}async function f(P,e,t=0){let{connectionMaxRetry:i,connectionRetryDelay:n}=e;try{return await P()}catch(s){if(s.code===h&&t<i)return await new Promise(o=>setTimeout(o,n)),f(P,e,t+1);throw s}}(function(){let P={debug:!0},e=null,t,i=!1;function n(...r){P.debug&&console.log("[browserController]",...r)}function s(...r){console.error("[browserController]",...r)}async function c(r,a){n("Connecting to CDP and attaching to target:",a),await o(r,a),n("Browser controller initialized with sessionId:",t)}async function o(r,a){try{e=new m({webSocketDebuggerUrl:r}),n("Attaching to target:",a,r);let{sessionId:g}=await e.Target.attachToTarget({targetId:a,flatten:!0});t=g,await e.Page.enable(void 0,t),await e.Runtime.enable(void 0,t),n("\u2713 Browser controller attached to target",a,"with sessionId:",t)}catch(g){throw s("Failed to connect to CDP and attach to target:",g),g}}async function d(r={}){try{if(!e||!t)throw new Error("CDP client not initialized. Call init() first.");n("Taking screenshot with options:",r);let a={format:r.format||"png",quality:r.quality||100,fromSurface:r.fromSurface!==!1,captureBeyondViewport:r.captureBeyondViewport!==!1};r.clip&&(a.clip={x:r.clip.x,y:r.clip.y,width:r.clip.width,height:r.clip.height,scale:r.clip.scale||1});let g=await e.Page.captureScreenshot(a,t);return n("Screenshot captured successfully, data length:",g.data?.length||0),{success:!0,data:g.data}}catch(a){let g=a instanceof Error?a.message:String(a);return s("Failed to take screenshot:",g),{success:!1,error:g}}}async function D(){try{if(!e||!t)throw new Error("CDP client not initialized. Call init() first.");n("Getting page info...");let r=await e.Runtime.evaluate({expression:`JSON.stringify({
          title: document.title,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })`},t),a=JSON.parse(r.result.value);return n("Page info retrieved:",a),{success:!0,data:a}}catch(r){let a=r instanceof Error?r.message:String(r);return s("Failed to get page info:",a),{success:!1,error:a}}}function C(){return{success:!0,data:{connected:!!e,sessionId:t,timestamp:Date.now()}}}globalThis.browserController={init:c,takeScreenshot:d,getPageInfo:D,getStatus:C}})();})();
