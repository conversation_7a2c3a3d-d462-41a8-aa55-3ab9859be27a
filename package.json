{"name": "pup", "version": "1.0.0", "description": "", "type": "module", "main": "pup.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node pup.js"}, "author": "", "license": "ISC", "dependencies": {"@browserbasehq/sdk": "^2.6.0", "@hyperbrowser/sdk": "^0.46.0", "cors": "^2.8.5", "express": "^5.1.0", "http": "^0.0.1-security", "http-proxy": "^1.18.1", "puppeteer": "24.10.0", "puppeteer-page-proxy": "^1.3.0", "puppeteer-proxy": "^1.0.3", "simple-cdp": "^1.8.6", "ws": "^8.18.2"}}