/**
 * Google Login Automation using Simple CDP
 *
 * This script automates the Google login process:
 * 1. Navigate to accounts.google.com
 * 2. Enter email: <EMAIL>
 * 3. Click Next
 * 4. Handle passkey screen by clicking Continue
 *
 * Prerequisites:
 * - Start Chrome with: chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 * - Or use: google-chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 */

import { createTarget, CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function automateGoogleLogin() {
  console.log("🚀 Starting Google Login Automation");

  try {
    // Navigate to Google accounts login page
    const url = "https://accounts.google.com";
    console.log(`📍 Creating target for: ${url}`);
    const targetInfo = await createTarget(url);
    console.log("✅ Target created:", targetInfo);

    // Create a CDP instance for the target
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    console.log("✅ Runtime domain enabled");

    await cdpInstance.Page.enable();
    console.log("✅ Page domain enabled");

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 4000));

    // Get page title to confirm we're on the right page
    const titleResult = await cdpInstance.Runtime.evaluate({
      expression: "document.title",
      returnByValue: true,
    });
    console.log("📄 Page title:", titleResult.result.value);

    // Step 1: Find and fill the email input field
    console.log("1️⃣ Looking for email input field...");

    // Wait for email input to be available
    const emailInputFound = await waitForElement(
      cdpInstance,
      'input[type="email"]',
      10000
    );
    if (!emailInputFound) {
      throw new Error("Email input field not found");
    }

    console.log("✅ Email input field found");

    // Click on the email input field
    await clickElement(cdpInstance, 'input[type="email"]');
    console.log("✅ Clicked on email input field");

    // Type the email address
    const email = "<EMAIL>";
    console.log(`⌨️ Typing email: ${email}`);
    await typeText(cdpInstance, email);

    // Step 2: Click the Next button
    console.log("2️⃣ Looking for Next button...");

    // Wait a moment for the form to process the email
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Find and click the Next button by text content
    const nextButtonFound = await waitForElementByText(
      cdpInstance,
      "button",
      "Next",
      5000
    );
    if (!nextButtonFound) {
      throw new Error("Next button not found");
    }

    console.log("✅ Next button found");
    await clickElementByText(cdpInstance, "button", "Next");
    console.log("✅ Clicked Next button");

    // Step 3: Wait for passkey screen and handle it
    console.log("3️⃣ Waiting for passkey screen...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Look for passkey continue button
    console.log("🔑 Looking for passkey Continue button...");

    // Try to find Continue button by text content first
    let continueButtonFound = await waitForElementByText(
      cdpInstance,
      "button",
      "Continue",
      3000
    );

    console.log("✅ Continue button found by text");
    await clickElementByText(cdpInstance, "button", "Continue");
    console.log("✅ Clicked Continue button");

    console.log("🎉 Google login automation completed!");
  } catch (error) {
    console.error("❌ Error during Google login automation:", error);
  }
}

// Helper function to wait for an element to appear
async function waitForElement(cdpInstance, selector, timeout = 5000) {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const result = await cdpInstance.Runtime.evaluate({
        expression: `document.querySelector('${selector}') !== null`,
        returnByValue: true,
      });

      if (result && result.result && result.result.value === true) {
        return true;
      }
    } catch (error) {
      console.log(`Error checking for element ${selector}:`, error.message);
      // Continue waiting
    }

    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  console.log(`⏰ Timeout waiting for element: ${selector}`);
  return false;
}

// Helper function to wait for an element by text content
async function waitForElementByText(
  cdpInstance,
  tagName,
  text,
  timeout = 5000
) {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const result = await cdpInstance.Runtime.evaluate({
        expression: `
          Array.from(document.querySelectorAll('${tagName}')).some(elem =>
            elem.textContent && elem.textContent.trim() === '${text}'
          )
        `,
        returnByValue: true,
      });

      if (result && result.result && result.result.value === true) {
        return true;
      }
    } catch (error) {
      console.log(
        `Error checking for element ${tagName} with text "${text}":`,
        error.message
      );
      // Continue waiting
    }

    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  console.log(`⏰ Timeout waiting for ${tagName} with text: ${text}`);
  return false;
}

// Helper function to click an element
async function clickElement(cdpInstance, selector) {
  // Get element position
  const elementInfo = await cdpInstance.Runtime.evaluate({
    expression: `
      (function() {
        const element = document.querySelector('${selector}');
        if (element) {
          const rect = element.getBoundingClientRect();
          return JSON.stringify({
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2,
            found: true
          });
        } else {
          return JSON.stringify({ found: false });
        }
      })()
    `,
    returnByValue: true,
  });

  console.log("Element info result:", JSON.stringify(elementInfo, null, 2));

  if (!elementInfo.result || !elementInfo.result.value) {
    throw new Error(`Failed to get element info for: ${selector}`);
  }

  const elementData = JSON.parse(elementInfo.result.value);

  if (!elementData.found) {
    throw new Error(`Element not found: ${selector}`);
  }

  const { x, y } = elementData;

  // Click the element
  await cdpInstance.Input.dispatchMouseEvent({
    type: "mousePressed",
    x: Math.round(x),
    y: Math.round(y),
    button: "left",
    clickCount: 1,
  });

  await cdpInstance.Input.dispatchMouseEvent({
    type: "mouseReleased",
    x: Math.round(x),
    y: Math.round(y),
    button: "left",
    clickCount: 1,
  });

  // Small delay after click
  await new Promise((resolve) => setTimeout(resolve, 300));
}

// Helper function to click an element by text content
async function clickElementByText(cdpInstance, tagName, text) {
  // Get element position
  const elementInfo = await cdpInstance.Runtime.evaluate({
    expression: `
      (function() {
        const elements = Array.from(document.querySelectorAll('${tagName}'));
        const element = elements.find(el =>
          el.textContent && el.textContent.trim() === '${text}'
        );
        if (element) {
          const rect = element.getBoundingClientRect();
          return JSON.stringify({
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2,
            found: true
          });
        } else {
          return JSON.stringify({ found: false });
        }
      })()
    `,
    returnByValue: true,
  });

  console.log(
    `Element info result for ${tagName} with text "${text}":`,
    JSON.stringify(elementInfo, null, 2)
  );

  if (!elementInfo.result || !elementInfo.result.value) {
    throw new Error(
      `Failed to get element info for: ${tagName} with text "${text}"`
    );
  }

  const elementData = JSON.parse(elementInfo.result.value);

  if (!elementData.found) {
    throw new Error(`Element not found: ${tagName} with text "${text}"`);
  }

  const { x, y } = elementData;

  // Click the element
  await cdpInstance.Input.dispatchMouseEvent({
    type: "mousePressed",
    x: Math.round(x),
    y: Math.round(y),
    button: "left",
    clickCount: 1,
  });

  await cdpInstance.Input.dispatchMouseEvent({
    type: "mouseReleased",
    x: Math.round(x),
    y: Math.round(y),
    button: "left",
    clickCount: 1,
  });

  // Small delay after click
  await new Promise((resolve) => setTimeout(resolve, 300));
}

// Helper function to type text
async function typeText(cdpInstance, text) {
  for (const char of text) {
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      text: char,
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      text: char,
    });
    // Small delay between characters for more natural typing
    await new Promise((resolve) => setTimeout(resolve, 50));
  }
}

// Run the automation
automateGoogleLogin().catch(console.error);
