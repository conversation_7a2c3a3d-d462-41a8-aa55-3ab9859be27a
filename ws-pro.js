// Import WebSocket (native in modern Node.js or use 'ws' for older versions or browser-like API)
import WebSocket from "ws";

// Replace this with your WebSocket URL
const WS_URL =
  "wss://connect.hyperbrowser.ai/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOiIwMDQ5ZjdhNC0xY2JlLTRjNDktOGFlYi04ZTMxNWQ1ZmFhNGUiLCJ0ZWFtSWQiOiJjZGM2NTJiMy1mMzc0LTRlY2QtOTE4My01MmNmYmJlNzgxNTMiLCJpYXQiOjE3NTA4Nzk5MDIsImV4cCI6MTc1MDkyMzEwMn0.-L8-LOxHZzk_Z0ZBEToYHs4zdv4-u_mYRj6Z8HDPMbs&keepAlive=true"; // public echo server

// Create a new WebSocket connection
const ws = new WebSocket(WS_URL);

// Event: connection opened
ws.on("open", () => {
  console.log("✅ Connected to WebSocket server");

  // Wait a second before closing
  // setTimeout(() => {
  //   console.log("🔒 Closing WebSocket connection");
  //   ws.close();
  // }, 1000);
});

// Event: connection closed
ws.on("close", () => {
  console.log("❌ WebSocket connection closed");
});

// Event: error
ws.on("error", (error) => {
  console.error("❗ WebSocket error:", error);
});
