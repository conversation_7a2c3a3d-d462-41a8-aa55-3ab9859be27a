/**
 * Google Login Automation for Remote Browsers (Hyperbrowser/Browserbase)
 *
 * This script automates the Google login process using remote browsers:
 * 1. Navigate to accounts.google.com
 * 2. Enter email: <EMAIL>
 * 3. Click Next
 * 4. Handle passkey screen with WebAuthenticationProxy
 */

import { createTarget, CDP } from "simple-cdp";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import { Browserbase } from "@browserbasehq/sdk";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

// Configuration
const BROWSERBASE_API_KEY = "bb_live_QwIt78tzGs_zl31pDrEpnMCZ-lI";
const BROWSERBASE_PROJECT_ID = "8965aab4-7075-4993-9f3b-244157f0bd92";
const HYPERBROWSER_API_KEY = "hb_28aac10409666bbccf859a9b8804";

async function automateGoogleLoginRemote() {
  console.log("🚀 Starting Google Login Automation (Remote Browser)");

  // Configuration - change this to switch between providers
  const browserType = "hyperbrowser"; // "hyperbrowser" or "browserbase"
  const isLocal = true; // Set to true for local browser testing
  let wsEndpoint;
  let sessionId = null;

  try {
    if (isLocal) {
      // For local testing - connect to existing Chrome instance
      console.log("🔗 Connecting to local Chrome instance...");
      const res = await fetch("http://localhost:9222/json/version");
      const { webSocketDebuggerUrl } = await res.json();
      wsEndpoint = webSocketDebuggerUrl;
      console.log("✅ Local Chrome connection established");
    } else {
      // Initialize remote browser session
      if (browserType === "browserbase") {
        console.log("🌐 Initializing Browserbase session...");
        const bb = new Browserbase({
          apiKey: BROWSERBASE_API_KEY,
        });
        const session = await bb.sessions.create({
          projectId: BROWSERBASE_PROJECT_ID,
          keepAlive: true,
        });
        wsEndpoint = session.connectUrl;
        console.log("✅ Browserbase session created");
      } else if (browserType === "hyperbrowser") {
        console.log("🌐 Initializing Hyperbrowser session...");
        const hyperBrowser = new Hyperbrowser({
          apiKey: HYPERBROWSER_API_KEY,
          timeout: 60000,
        });
        const session = await hyperBrowser.sessions.create({
          browserArgs: ["--auto-accept-this-tab-capture"],
          device: ["desktop"],
        });
        wsEndpoint = session.wsEndpoint;
        console.log("✅ Hyperbrowser session created");
      }
    }

    console.log("🔗 WebSocket endpoint:", wsEndpoint);

    // Create CDP connection to remote browser
    const cdpInstance = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    // For remote browsers, we need to attach to a target and get sessionId
    console.log("📋 Getting list of targets...");
    const targets = await cdpInstance.Target.getTargets();
    console.log("🎯 Available targets:", targets.targetInfos.length);

    // Find a page target
    const pageTarget = targets.targetInfos.find(
      (target) => target.type === "page"
    );
    if (!pageTarget) {
      throw new Error("No page target found");
    }

    console.log("📍 Found page target:", pageTarget.targetId);

    // Attach to the target
    const attachResult = await cdpInstance.Target.attachToTarget({
      targetId: pageTarget.targetId,
      flatten: true,
    });

    sessionId = attachResult.sessionId;
    console.log("✅ Attached to target with sessionId:", sessionId);

    // Enable necessary domains (with sessionId for remote browsers)
    await cdpInstance.Runtime.enable(null, sessionId);
    console.log("✅ Runtime domain enabled");

    await cdpInstance.Page.enable(null, sessionId);
    console.log("✅ Page domain enabled");

    // Input domain might not be available in all remote browsers
    try {
      await cdpInstance.Input.enable(null, sessionId);
      console.log("✅ Input domain enabled");
    } catch (error) {
      console.log(
        "⚠️ Input domain not available, will use JavaScript-based interactions"
      );
    }

    // Navigate to Google accounts login page
    const url = "https://accounts.google.com";
    console.log(`📍 Navigating to: ${url}`);
    await cdpInstance.Page.navigate({ url }, sessionId);

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Get page title to confirm we're on the right page
    const titleResult = await cdpInstance.Runtime.evaluate(
      {
        expression: "document.title",
        returnByValue: true,
      },
      sessionId
    );
    console.log("📄 Page title:", titleResult.result.value);

    // Step 1: Find and fill the email input field
    console.log("1️⃣ Looking for email input field...");

    // Wait for email input to be available
    const emailInputFound = await waitForElement(
      cdpInstance,
      'input[type="email"]',
      10000,
      sessionId
    );
    if (!emailInputFound) {
      throw new Error("Email input field not found");
    }

    console.log("✅ Email input field found");

    // Click on the email input field
    await clickElement(cdpInstance, 'input[type="email"]', sessionId);
    console.log("✅ Clicked on email input field");

    // Type the email address
    const email = "<EMAIL>";
    console.log(`⌨️ Typing email: ${email}`);
    await typeText(cdpInstance, email, sessionId);

    // Step 2: Click the Next button
    console.log("2️⃣ Looking for Next button...");

    // Wait a moment for the form to process the email
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Find and click the Next button by text content
    const nextButtonFound = await waitForElementByText(
      cdpInstance,
      "button",
      "Next",
      5000,
      sessionId
    );
    if (!nextButtonFound) {
      throw new Error("Next button not found");
    }

    console.log("✅ Next button found");
    await clickElementByText(cdpInstance, "button", "Next", sessionId);
    console.log("✅ Clicked Next button");

    // Step 3: Wait for passkey screen and handle it
    console.log("3️⃣ Waiting for passkey screen...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Install WebAuthenticationProxy extension for passkey handling
    console.log("🔑 Setting up WebAuthenticationProxy for passkey handling...");
    await setupWebAuthenticationProxy(cdpInstance, sessionId);

    // Set up communication channel with local proxy server
    console.log("🌐 Setting up local proxy communication...");
    await setupLocalProxyConnection(cdpInstance, sessionId);

    // Look for passkey continue button
    console.log("🔑 Looking for passkey Continue button...");

    // Try to find Continue button by text content first
    let continueButtonFound = await waitForElementByText(
      cdpInstance,
      "button",
      "Continue",
      5000,
      sessionId
    );

    if (continueButtonFound) {
      console.log("✅ Continue button found");
      // The WebAuthenticationProxy will handle the passkey authentication
      await clickElementByText(cdpInstance, "button", "Continue", sessionId);
      console.log(
        "✅ Clicked Continue button - WebAuthenticationProxy will handle passkey"
      );

      // Wait for authentication to complete
      console.log("⏳ Waiting for passkey authentication...");
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }

    console.log("🎉 Google login automation completed!");
  } catch (error) {
    console.error("❌ Error during Google login automation:", error);
  }
}

// Setup WebAuthenticationProxy for remote passkey authentication
async function setupWebAuthenticationProxy(cdpInstance, sessionId = null) {
  try {
    console.log("🔧 Installing WebAuthenticationProxy extension...");

    // Inject the WebAuthenticationProxy script
    await cdpInstance.Runtime.evaluate(
      {
        expression: `
        (function() {
          // WebAuthenticationProxy implementation
          if (!window.webAuthenticationProxy) {
            window.webAuthenticationProxy = {
              isEnabled: true,

              // Override navigator.credentials.get for passkey requests
              originalGet: navigator.credentials.get.bind(navigator.credentials),

              async handlePasskeyRequest(options) {
                console.log('🔑 Passkey request intercepted in remote browser:', options);

                try {
                  // Send request to local proxy server
                  const response = await fetch('http://localhost:8080/webauthn/get', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      options: options,
                      origin: window.location.origin,
                      timestamp: Date.now()
                    })
                  });

                  if (!response.ok) {
                    throw new Error('Local proxy server not available. Please start the proxy server on your local machine.');
                  }

                  const result = await response.json();

                  if (!result.success) {
                    throw new Error(result.error || 'Authentication failed');
                  }

                  console.log('✅ Passkey authentication successful via local proxy');
                  return result.credential;

                } catch (error) {
                  console.error('❌ Local proxy authentication failed:', error);

                  // Show user-friendly message
                  alert('Passkey authentication requires your local device. Please ensure the WebAuth proxy server is running on your local machine (localhost:8080).');
                  throw error;
                }
              }
            };

            // Override navigator.credentials.get
            navigator.credentials.get = async function(options) {
              if (options.publicKey) {
                console.log('🔑 Intercepting passkey request');
                return window.webAuthenticationProxy.handlePasskeyRequest(options);
              }
              return window.webAuthenticationProxy.originalGet(options);
            };

            console.log('✅ WebAuthenticationProxy installed');
          }
        })()
      `,
        returnByValue: true,
      },
      sessionId
    );

    console.log("✅ WebAuthenticationProxy extension installed");
  } catch (error) {
    console.error("❌ Failed to setup WebAuthenticationProxy:", error);
  }
}

// Setup local proxy connection for WebAuthn forwarding
async function setupLocalProxyConnection(cdpInstance, sessionId = null) {
  try {
    console.log("🔧 Setting up local proxy connection...");

    // Test connection to local proxy server
    await cdpInstance.Runtime.evaluate(
      {
        expression: `
        (async function() {
          try {
            const response = await fetch('http://localhost:8080/status');
            if (response.ok) {
              const status = await response.json();
              console.log('✅ Local proxy server is running:', status);
              return JSON.stringify({ success: true, status: status });
            } else {
              throw new Error('Proxy server not responding');
            }
          } catch (error) {
            console.warn('⚠️ Local proxy server not available:', error.message);
            console.warn('Please start the proxy server: node webauth-proxy-server.js');
            return JSON.stringify({ success: false, error: error.message });
          }
        })()
      `,
        returnByValue: true,
        awaitPromise: true,
      },
      sessionId
    );

    console.log("✅ Local proxy connection setup complete");
  } catch (error) {
    console.error("❌ Failed to setup local proxy connection:", error);
  }
}

// Helper function to wait for an element to appear
async function waitForElement(
  cdpInstance,
  selector,
  timeout = 5000,
  sessionId = null
) {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const result = await cdpInstance.Runtime.evaluate(
        {
          expression: `document.querySelector('${selector}') !== null`,
          returnByValue: true,
        },
        sessionId
      );

      if (result && result.result && result.result.value === true) {
        return true;
      }
    } catch (error) {
      console.log(`Error checking for element ${selector}:`, error.message);
    }

    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  console.log(`⏰ Timeout waiting for element: ${selector}`);
  return false;
}

// Helper function to wait for an element by text content
async function waitForElementByText(
  cdpInstance,
  tagName,
  text,
  timeout = 5000,
  sessionId = null
) {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      const result = await cdpInstance.Runtime.evaluate(
        {
          expression: `
          Array.from(document.querySelectorAll('${tagName}')).some(elem =>
            elem.textContent && elem.textContent.trim() === '${text}'
          )
        `,
          returnByValue: true,
        },
        sessionId
      );

      if (result && result.result && result.result.value === true) {
        return true;
      }
    } catch (error) {
      console.log(
        `Error checking for element ${tagName} with text "${text}":`,
        error.message
      );
    }

    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  console.log(`⏰ Timeout waiting for ${tagName} with text: ${text}`);
  return false;
}

// Helper function to click an element
async function clickElement(cdpInstance, selector, sessionId = null) {
  // Use JavaScript-based clicking for better compatibility
  const result = await cdpInstance.Runtime.evaluate(
    {
      expression: `
      (function() {
        const element = document.querySelector('${selector}');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.focus();
          element.click();
          return JSON.stringify({ success: true, found: true });
        } else {
          return JSON.stringify({ success: false, found: false });
        }
      })()
    `,
      returnByValue: true,
    },
    sessionId
  );

  if (!result.result || !result.result.value) {
    throw new Error(`Failed to click element: ${selector}`);
  }

  const resultData = JSON.parse(result.result.value);

  if (!resultData.found) {
    throw new Error(`Element not found: ${selector}`);
  }

  if (!resultData.success) {
    throw new Error(`Failed to click element: ${selector}`);
  }

  await new Promise((resolve) => setTimeout(resolve, 300));
}

// Helper function to click an element by text content
async function clickElementByText(
  cdpInstance,
  tagName,
  text,
  sessionId = null
) {
  // Use JavaScript-based clicking for better compatibility
  const result = await cdpInstance.Runtime.evaluate(
    {
      expression: `
      (function() {
        const elements = Array.from(document.querySelectorAll('${tagName}'));
        const element = elements.find(el =>
          el.textContent && el.textContent.trim() === '${text}'
        );
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.focus();
          element.click();
          return JSON.stringify({ success: true, found: true });
        } else {
          return JSON.stringify({ success: false, found: false });
        }
      })()
    `,
      returnByValue: true,
    },
    sessionId
  );

  if (!result.result || !result.result.value) {
    throw new Error(`Failed to click element: ${tagName} with text "${text}"`);
  }

  const resultData = JSON.parse(result.result.value);

  if (!resultData.found) {
    throw new Error(`Element not found: ${tagName} with text "${text}"`);
  }

  if (!resultData.success) {
    throw new Error(`Failed to click element: ${tagName} with text "${text}"`);
  }

  await new Promise((resolve) => setTimeout(resolve, 300));
}

// Helper function to type text
async function typeText(cdpInstance, text, sessionId = null) {
  // Use JavaScript-based typing for better compatibility
  const result = await cdpInstance.Runtime.evaluate(
    {
      expression: `
      (function() {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
          activeElement.value = '${text}';
          activeElement.dispatchEvent(new Event('input', { bubbles: true }));
          activeElement.dispatchEvent(new Event('change', { bubbles: true }));
          return JSON.stringify({ success: true });
        } else {
          return JSON.stringify({ success: false, error: 'No active input element' });
        }
      })()
    `,
      returnByValue: true,
    },
    sessionId
  );

  if (!result.result || !result.result.value) {
    throw new Error(`Failed to type text: ${text}`);
  }

  const resultData = JSON.parse(result.result.value);

  if (!resultData.success) {
    throw new Error(
      `Failed to type text: ${resultData.error || "Unknown error"}`
    );
  }

  await new Promise((resolve) => setTimeout(resolve, 300));
}

// Run the automation
automateGoogleLoginRemote().catch(console.error);
