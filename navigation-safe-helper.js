/**
 * Navigation-Safe Browser Controller Helper
 *
 * This script provides a safe way to control browser interactions without
 * creating CDP connections from within the page, which can cause browser
 * crashes in cloud environments like Hyperbrowser/Browserbase.
 *
 * Architecture:
 * 1. Helper tab - Receives messages and executes actions
 * 2. Client script - Sends messages from main page
 * 3. BroadcastChannel - Communication between tabs
 * 4. Backend Puppeteer - Handles all CDP operations
 */

/**
 * Helper Tab Script
 * Runs in a separate tab and handles incoming messages
 */
function createHelperTabScript() {
  return `
    console.log("🤖 Navigation-Safe Helper Tab loaded");
    
    // Create BroadcastChannel for communication
    const channel = new BroadcastChannel('browser-controller');
    
    // Message handler interface
    const messageHandlers = {
      async click(data) {
        console.log("🖱️ Helper: Executing click at", data.x, data.y);
        try {
          // Create and dispatch click events
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: data.x,
            clientY: data.y
          });
          
          const element = document.elementFromPoint(data.x, data.y);
          if (element) {
            element.dispatchEvent(clickEvent);
            return { 
              success: true, 
              element: element.tagName,
              className: element.className,
              id: element.id
            };
          }
          return { success: false, error: "No element found at coordinates" };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      async querySelector(data) {
        console.log("🔍 Helper: Querying selector", data.selector);
        try {
          const element = document.querySelector(data.selector);
          return { 
            success: true, 
            found: !!element,
            tagName: element?.tagName,
            className: element?.className,
            id: element?.id,
            text: element?.textContent?.slice(0, 100),
            href: element?.href
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      async querySelectorAll(data) {
        console.log("🔍 Helper: Querying all selectors", data.selector);
        try {
          const elements = document.querySelectorAll(data.selector);
          return { 
            success: true, 
            count: elements.length,
            elements: Array.from(elements).slice(0, 10).map(el => ({
              tagName: el.tagName,
              className: el.className,
              id: el.id,
              text: el.textContent?.slice(0, 50)
            }))
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      async getPageInfo(data) {
        console.log("📋 Helper: Getting page info");
        return {
          success: true,
          url: window.location.href,
          title: document.title,
          readyState: document.readyState,
          userAgent: navigator.userAgent,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          }
        };
      },
      
      async injectScript(data) {
        console.log("💉 Helper: Injecting script");
        try {
          const result = eval(data.code);
          return { success: true, result };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      async waitForSelector(data) {
        console.log("⏳ Helper: Waiting for selector", data.selector);
        try {
          const timeout = data.timeout || 5000;
          const startTime = Date.now();
          
          while (Date.now() - startTime < timeout) {
            const element = document.querySelector(data.selector);
            if (element) {
              return { 
                success: true, 
                found: true,
                tagName: element.tagName,
                text: element.textContent?.slice(0, 100)
              };
            }
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          
          return { success: false, error: "Timeout waiting for selector" };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      async scrollTo(data) {
        console.log("📜 Helper: Scrolling to", data.x, data.y);
        try {
          window.scrollTo(data.x || 0, data.y || 0);
          return { success: true };
        } catch (error) {
          return { success: false, error: error.message };
        }
      }
    };
    
    // Listen for messages
    channel.addEventListener('message', async (event) => {
      const { id, type, data } = event.data;
      console.log("📨 Helper: Received message", { id, type, data });
      
      try {
        let result;
        if (messageHandlers[type]) {
          result = await messageHandlers[type](data);
        } else {
          result = { success: false, error: \`Unknown message type: \${type}\` };
        }
        
        // Send response back
        channel.postMessage({
          id,
          type: 'response',
          result
        });
        
      } catch (error) {
        console.error("💥 Helper: Error handling message", error);
        channel.postMessage({
          id,
          type: 'response',
          result: { success: false, error: error.message }
        });
      }
    });
    
    console.log("✅ Navigation-Safe Helper Tab ready");
  `;
}

/**
 * Client Script
 * Runs in the main page and sends messages to helper tab
 */
function createClientScript() {
  return `
    console.log("📱 Navigation-Safe Client loaded");
    
    // Create BroadcastChannel for communication
    const channel = new BroadcastChannel('browser-controller');
    let messageId = 0;
    const pendingMessages = new Map();
    
    // Listen for responses
    channel.addEventListener('message', (event) => {
      const { id, type, result } = event.data;
      if (type === 'response' && pendingMessages.has(id)) {
        const { resolve } = pendingMessages.get(id);
        pendingMessages.delete(id);
        resolve(result);
      }
    });
    
    // Helper function to send messages and wait for response
    function sendMessage(type, data, timeout = 10000) {
      return new Promise((resolve, reject) => {
        const id = ++messageId;
        
        // Store pending message
        pendingMessages.set(id, { resolve, reject });
        
        // Set timeout
        setTimeout(() => {
          if (pendingMessages.has(id)) {
            pendingMessages.delete(id);
            reject(new Error(\`Message timeout: \${type}\`));
          }
        }, timeout);
        
        // Send message
        channel.postMessage({ id, type, data });
      });
    }
    
    // Expose navigation-safe browser controller API
    window.browserController = {
      async init() {
        console.log("🚀 Navigation-safe browser controller initialized");
        return { success: true };
      },
      
      async click(x, y) {
        return await sendMessage('click', { x, y });
      },
      
      async querySelector(selector) {
        return await sendMessage('querySelector', { selector });
      },
      
      async querySelectorAll(selector) {
        return await sendMessage('querySelectorAll', { selector });
      },
      
      async getPageInfo() {
        return await sendMessage('getPageInfo', {});
      },
      
      async injectScript(code) {
        return await sendMessage('injectScript', { code });
      },
      
      async waitForSelector(selector, timeout = 5000) {
        return await sendMessage('waitForSelector', { selector, timeout });
      },
      
      async scrollTo(x = 0, y = 0) {
        return await sendMessage('scrollTo', { x, y });
      },
      
      async takeScreenshot() {
        // Screenshots must be handled by backend Puppeteer
        console.log("📸 Screenshot requests should be handled by backend Puppeteer");
        return { success: false, error: "Use backend Puppeteer for screenshots" };
      }
    };
    
    console.log("✅ Navigation-safe browser controller ready");
  `;
}

// Export for use in other scripts
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    createHelperTabScript,
    createClientScript,
  };
}

// Global functions for browser use
if (typeof window !== "undefined") {
  window.NavigationSafeController = {
    createHelperTabScript,
    createClientScript,
  };
}
