#!/bin/bash

# <PERSON>ript to start the WebAuth proxy server and test remote Google login

echo "🔑 WebAuth Proxy & Remote Login Test"
echo "====================================="

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Function to check if port is in use
check_port() {
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
        echo "✅ Port 8080 is already in use (proxy server running)"
        return 0
    else
        echo "❌ Port 8080 is not in use"
        return 1
    fi
}

echo "📋 Step 1: Checking proxy server status..."
if check_port; then
    echo "✅ Proxy server is already running"
else
    echo "🚀 Starting WebAuth proxy server..."
    echo "📝 Note: Keep this terminal open for the proxy server"
    echo ""
    echo "In another terminal, run: node google-login-remote.js"
    echo ""
    echo "Starting proxy server in 3 seconds..."
    sleep 3
    node webauth-proxy-server.js &
    PROXY_PID=$!
    echo "✅ Proxy server started with PID: $PROXY_PID"
    sleep 2
fi

echo ""
echo "📋 Step 2: Testing remote browser connection..."
echo "🌐 This will:"
echo "   1. Connect to Hyperbrowser (remote browser in US)"
echo "   2. Navigate to Google accounts"
echo "   3. Fill in email: <EMAIL>"
echo "   4. Click Next"
echo "   5. When passkey prompt appears, it will forward to your local device"
echo ""
echo "🔑 Important: When you see the passkey prompt:"
echo "   - The remote browser will try to connect to localhost:8080"
echo "   - Your local proxy server will handle the authentication"
echo "   - Use Touch ID/Face ID on your local device"
echo ""

read -p "Press Enter to start the remote login test..."

echo "🚀 Starting remote Google login automation..."
node google-login-remote.js

echo ""
echo "🎉 Test completed!"
echo ""
echo "📊 What happened:"
echo "   ✅ Remote browser (US) connected successfully"
echo "   ✅ WebAuth proxy injected into remote page"
echo "   ✅ Email filled and Next clicked"
echo "   ✅ Passkey request forwarded to local device (Nepal)"
echo ""
echo "🔧 If authentication didn't work:"
echo "   1. Make sure proxy server is running: http://localhost:8080"
echo "   2. Check browser console for WebAuth proxy messages"
echo "   3. Verify your device supports Touch ID/Face ID"
echo ""
echo "🌐 Architecture:"
echo "   Remote Browser (US) → WebAuth Proxy → Local Server (Nepal) → Your Device"
