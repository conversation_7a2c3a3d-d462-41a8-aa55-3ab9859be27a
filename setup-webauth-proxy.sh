#!/bin/bash

# WebAuthentication Proxy Setup Script
# This script sets up the complete WebAuth proxy system for remote passkey authentication

echo "🔑 WebAuthentication Proxy Setup"
echo "================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are available"

# Install required dependencies
echo "📦 Installing dependencies..."
npm install express cors @hyperbrowser/sdk @browserbasehq/sdk

# Create package.json if it doesn't exist
if [ ! -f "package.json" ]; then
    echo "📝 Creating package.json..."
    cat > package.json << EOF
{
  "name": "webauth-proxy",
  "version": "1.0.0",
  "description": "WebAuthentication Proxy for remote browsers",
  "type": "module",
  "main": "webauth-proxy-server.js",
  "scripts": {
    "start": "node webauth-proxy-server.js",
    "google-login-local": "node google-login-cdp.js",
    "google-login-remote": "node google-login-remote.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "@hyperbrowser/sdk": "latest",
    "@browserbasehq/sdk": "latest",
    "simple-cdp": "latest",
    "ws": "^8.14.2"
  }
}
EOF
fi

echo "✅ Dependencies installed"

# Make scripts executable
chmod +x setup-webauth-proxy.sh

echo ""
echo "🎉 Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo ""
echo "1. Start the WebAuth Proxy Server:"
echo "   npm start"
echo "   (or: node webauth-proxy-server.js)"
echo ""
echo "2. Install the Chrome Extension:"
echo "   - Open Chrome and go to chrome://extensions/"
echo "   - Enable 'Developer mode'"
echo "   - Click 'Load unpacked'"
echo "   - Select the 'webauth-proxy-extension' folder"
echo ""
echo "3. Run Google Login Automation:"
echo "   - For local browser: npm run google-login-local"
echo "   - For remote browser: npm run google-login-remote"
echo ""
echo "🔧 Configuration:"
echo "   - Edit google-login-remote.js to switch between Hyperbrowser/Browserbase"
echo "   - Change browserType variable: 'hyperbrowser' or 'browserbase'"
echo ""
echo "📖 Documentation:"
echo "   - WebAuth Proxy Server: http://localhost:8080"
echo "   - Extension popup shows status and logs"
echo ""
echo "🚀 Ready to use remote passkey authentication!"
