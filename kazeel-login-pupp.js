/**
 * Puppeteer Script - Navigate to Kazeel Login Page
 *
 * This script uses P<PERSON>peteer to navigate to dev-tasks.kazeel.com/auth/login
 *
 * Prerequisites:
 * - npm install puppeteer (or use @cloudflare/puppeteer if available)
 */

import puppeteer from "puppeteer";

async function navigateToKazeelLogin() {
  const res = await fetch("http://localhost:9222/json/version");
  const { webSocketDebuggerUrl } = await res.json();

  // Launch browser
  const browser = await puppeteer.connect({
    browserWSEndpoint: webSocketDebuggerUrl,
    headless: false,
    defaultViewport: null,
  });

  try {
    // Create new page
    const page = await browser.newPage();

    // Set viewport to 1024x768
    // await page.setViewport({
    //   width: 1024,
    //   height: 768,
    //   deviceScaleFactor: 1,
    // });

    // Navigate to Kazeel login page
    const url =
      "https://dev-tasks.kazeel.com/auth/captcha?email=dirtynoobs%40yahoo.com&captchaText=2E9IX&captchaAnswer=2E9IX";
    console.log(`📍 Navigating to: ${url}`);

    await page.goto(url, { waitUntil: "networkidle2" });
    console.log("✅ Navigation completed");

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page title
    const title = await page.title();
    console.log("📄 Page title:", title);
    await page.setViewport({
      width: 1024,
      height: 768,
      deviceScaleFactor: 1,
    });

    // Request screen sharing
    await page.evaluate(async () => {
      console.log("🎥 Requesting screen sharing...");
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });
      window.screenStream = screenStream;
      console.log("✅ Screen sharing started successfully", {
        width: window.innerWidth,
        height: window.innerHeight,
      });
    });

    await page.setViewport({
      width: 1024,
      height: 768,
    });
    // set window bounds with puppeteer
    await page.evaluate(() => {
      window.resizeTo(1024, 768);
    });
    // await page.setViewport({
    //   width: 1024,
    //   height: 768,
    //   deviceScaleFactor: 1,
    // });
    // await page.setViewport({
    //   width: 1024,
    //   height: 768,
    //   deviceScaleFactor: 1,
    // });
    // await page.setViewport({
    //   width: 1024,
    //   height: 768,
    //   deviceScaleFactor: 1,
    // });
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    // Uncomment the line below if you want the browser to close automatically
    // await browser.close();
  }
}

// Run the script
navigateToKazeelLogin().catch(console.error);
