import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import { Browserbase } from "@browserbasehq/sdk";

const BROWSERBASE_API_KEY = "bb_live_QwIt78tzGs_zl31pDrEpnMCZ-lI";
const BROWSERBASE_PROJECT_ID = "8965aab4-7075-4993-9f3b-244157f0bd92";

(async () => {
  let browser;
  let wsEndpoint;
  const browserType = "browserbase";
  const useLocalBrowser = false;
  const launchNew = true;
  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--auto-accept-this-tab-capture",
        ],
      });
    } else {
      const res = await fetch("http://localhost:9222/json/version");
      const { webSocketDebuggerUrl } = await res.json();

      browser = await puppeteer.connect({
        browserWSEndpoint: webSocketDebuggerUrl,
      });
    }
  } else {
    if (browserType == "browserbase") {
      const bb = new Browserbase({
        apiKey: BROWSERBASE_API_KEY,
      });
      const session = await bb.sessions.create({
        projectId: BROWSERBASE_PROJECT_ID,
      });
      wsEndpoint = session.connectUrl;
    } else if (browserType == "hyperbrowser") {
      const hyperBrowser = new Hyperbrowser({
        apiKey: "hb_28aac10409666bbccf859a9b8804",
        timeout: 60000,
      });
      const session = await hyperBrowser.sessions.create({
        browserArgs: ["--auto-accept-this-tab-capture"],
        device: ["desktop"],
      });
      wsEndpoint = session.wsEndpoint;
    }
    console.log({ wsEndpoint });
    browser = await puppeteer.connect({
      browserWSEndpoint: wsEndpoint,
    });
  }

  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080, deviceScaleFactor: 0 });
  await page.setBypassCSP(true);
  // Simple error monitoring - just console logs
  page.on("error", (error) => {
    console.error("💥 PAGE CRASH:", error.message);
  });

  page.on("pageerror", (error) => {
    console.error("💥 SCRIPT ERROR:", error.message);
  });

  page.on("close", () => {
    console.log("💥 Page closed");
  });

  browser.on("disconnected", () => {
    console.log("💥 Browser disconnected");
  });

  // Log browser console messages
  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
  });

  console.log("1️⃣ Navigating to kazeel...");
  await page.goto("https://dev-tasks.kazeel.com/auth/login", {
    waitUntil: "domcontentloaded",
  });

  console.log("2️⃣ Setting up navigation-safe browser controller...");
})();
